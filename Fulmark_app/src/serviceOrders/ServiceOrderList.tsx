import {
    List,
    Datagrid,
    TextField,
    DateField,
    ReferenceField,
    SelectField,
    NumberField,
    FunctionField,
    EditButton,
    ShowButton,
    DeleteButton,
    TopToolbar,
    CreateButton,
    ExportButton,
    FilterButton,
    SearchInput,
    SelectInput,
    DateInput,
    ReferenceInput,
    AutocompleteInput,
} from 'react-admin';
import { Chip, Box, Typography, Tabs, Tab } from '@mui/material';
import { useState } from 'react';
import { ServiceOrdersKanban } from './ServiceOrdersKanban';
import { ServiceOrderStatus, PriorityIndicator } from '../components/cosmic/CosmicStatusIndicator';
import { CosmicButton } from '../components/cosmic/CosmicButton';

const serviceOrderFilters = [
    <SearchInput source="q" alwaysOn />,
    <SelectInput
        source="status"
        choices={[
            { id: 'new', name: 'New' },
            { id: 'scheduled', name: 'Scheduled' },
            { id: 'in_progress', name: 'In Progress' },
            { id: 'completed', name: 'Completed' },
            { id: 'cancelled', name: 'Cancelled' },
        ]}
    />,
    <SelectInput
        source="priority"
        choices={[
            { id: 'low', name: 'Low' },
            { id: 'medium', name: 'Medium' },
            { id: 'high', name: 'High' },
            { id: 'urgent', name: 'Urgent' },
        ]}
    />,
    <SelectInput
        source="service_type"
        choices={[
            { id: 'installation', name: 'Installation' },
            { id: 'maintenance', name: 'Maintenance' },
            { id: 'repair', name: 'Repair' },
            { id: 'inspection', name: 'Inspection' },
        ]}
    />,
    <ReferenceInput source="technician_id" reference="technicians">
        <AutocompleteInput
            optionText={(choice: any) => `${choice.first_name} ${choice.last_name}`}
        />
    </ReferenceInput>,
    <DateInput source="scheduled_date_gte" label="Scheduled After" />,
    <DateInput source="scheduled_date_lte" label="Scheduled Before" />,
];

const ServiceOrderListActions = () => (
    <TopToolbar>
        <FilterButton />
        <CreateButton />
        <ExportButton />
    </TopToolbar>
);

const StatusField = ({ record }: { record?: any }) => {
    if (!record) return null;
    
    const statusColors: Record<string, string> = {
        new: '#2196f3',
        scheduled: '#ff9800',
        in_progress: '#4caf50',
        completed: '#8bc34a',
        cancelled: '#f44336',
    };

    return (
        <Chip
            label={record.status}
            style={{
                backgroundColor: statusColors[record.status] || '#757575',
                color: 'white',
                textTransform: 'capitalize',
            }}
            size="small"
        />
    );
};

const PriorityField = ({ record }: { record?: any }) => {
    if (!record) return null;
    
    const priorityColors: Record<string, string> = {
        low: '#4caf50',
        medium: '#ff9800',
        high: '#f44336',
        urgent: '#9c27b0',
    };

    return (
        <Chip
            label={record.priority}
            style={{
                backgroundColor: priorityColors[record.priority] || '#757575',
                color: 'white',
                textTransform: 'capitalize',
            }}
            size="small"
        />
    );
};

export const ServiceOrderList = () => (
    <List
        filters={serviceOrderFilters}
        actions={<ServiceOrderListActions />}
        sort={{ field: 'created_at', order: 'DESC' }}
        perPage={25}
    >
        <Datagrid rowClick="show" bulkActionButtons={false}>
            <TextField source="title" />
            <ReferenceField source="company_id" reference="companies" link="show">
                <TextField source="name" />
            </ReferenceField>
            <ReferenceField source="contact_id" reference="contacts" link="show">
                <FunctionField
                    render={(record: any) => `${record.first_name} ${record.last_name}`}
                />
            </ReferenceField>
            <SelectField
                source="service_type"
                choices={[
                    { id: 'installation', name: 'Installation' },
                    { id: 'maintenance', name: 'Maintenance' },
                    { id: 'repair', name: 'Repair' },
                    { id: 'inspection', name: 'Inspection' },
                ]}
            />
            <FunctionField source="status" render={StatusField} />
            <FunctionField source="priority" render={PriorityField} />
            <DateField source="scheduled_date" showTime />
            <ReferenceField source="technician_id" reference="technicians" link={false}>
                <FunctionField
                    render={(record: any) => 
                        record ? `${record.first_name} ${record.last_name}` : 'Unassigned'
                    }
                />
            </ReferenceField>
            <NumberField source="total_cost" options={{ style: 'currency', currency: 'PLN' }} />
            <DateField source="created_at" showTime />
            <ShowButton />
            <EditButton />
            <DeleteButton />
        </Datagrid>
    </List>
);
