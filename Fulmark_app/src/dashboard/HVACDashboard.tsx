import { Grid, Card, CardContent, Typography, Box, Chip, useTheme, alpha } from '@mui/material';
import {
    Build as ServiceIcon,
    AcUnit as EquipmentIcon,
    Engineering as TechnicianIcon,
    Warning as WarningIcon,
    CheckCircle as CheckIcon,
    Schedule as ScheduleIcon,
    TrendingUp as TrendingUpIcon,
    TrendingDown as TrendingDownIcon,
    AttachMoney as MoneyIcon,
    Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { useGetList } from 'react-admin';
import { ServiceOrder, Equipment, Technician, MaintenanceSchedule } from '../types';
import { CosmicCard, CosmicMetricCard } from '../components/cosmic/CosmicCard';
import { ServiceOrderStatus, PriorityIndicator, EquipmentStatus } from '../components/cosmic/CosmicStatusIndicator';

const StatCard = ({ 
    title, 
    value, 
    icon, 
    color = '#2196f3',
    subtitle 
}: {
    title: string;
    value: number | string;
    icon: React.ReactNode;
    color?: string;
    subtitle?: string;
}) => (
    <Card>
        <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                    <Typography color="textSecondary" gutterBottom variant="h6">
                        {title}
                    </Typography>
                    <Typography variant="h4" component="div">
                        {value}
                    </Typography>
                    {subtitle && (
                        <Typography variant="body2" color="textSecondary">
                            {subtitle}
                        </Typography>
                    )}
                </Box>
                <Box sx={{ color, fontSize: 40 }}>
                    {icon}
                </Box>
            </Box>
        </CardContent>
    </Card>
);

const ServiceOrdersOverview = () => {
    const { data: serviceOrders, total, isPending } = useGetList<ServiceOrder>('service_orders', {
        pagination: { page: 1, perPage: 1000 },
    });

    if (isPending) return <div>Loading...</div>;

    const statusCounts = serviceOrders?.reduce((acc, order) => {
        acc[order.status] = (acc[order.status] || 0) + 1;
        return acc;
    }, {} as Record<string, number>) || {};

    const urgentOrders = serviceOrders?.filter(order => order.priority === 'urgent').length || 0;
    const todayOrders = serviceOrders?.filter(order => {
        const today = new Date().toISOString().split('T')[0];
        return order.scheduled_date?.startsWith(today);
    }).length || 0;

    return (
        <Grid container spacing={2}>
            <Grid item xs={12} md={3}>
                <StatCard
                    title="Total Service Orders"
                    value={total || 0}
                    icon={<ServiceIcon />}
                    color="#2196f3"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <StatCard
                    title="New Orders"
                    value={statusCounts.new || 0}
                    icon={<ServiceIcon />}
                    color="#ff9800"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <StatCard
                    title="In Progress"
                    value={statusCounts.in_progress || 0}
                    icon={<ServiceIcon />}
                    color="#4caf50"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <StatCard
                    title="Urgent Orders"
                    value={urgentOrders}
                    icon={<WarningIcon />}
                    color="#f44336"
                />
            </Grid>
            <Grid item xs={12} md={6}>
                <StatCard
                    title="Scheduled Today"
                    value={todayOrders}
                    icon={<ScheduleIcon />}
                    color="#9c27b0"
                    subtitle="Orders scheduled for today"
                />
            </Grid>
            <Grid item xs={12} md={6}>
                <StatCard
                    title="Completed"
                    value={statusCounts.completed || 0}
                    icon={<CheckIcon />}
                    color="#4caf50"
                    subtitle="Successfully completed orders"
                />
            </Grid>
        </Grid>
    );
};

const EquipmentOverview = () => {
    const { data: equipment, total, isPending } = useGetList<Equipment>('equipment', {
        pagination: { page: 1, perPage: 1000 },
    });

    if (isPending) return <div>Loading...</div>;

    const statusCounts = equipment?.reduce((acc, item) => {
        acc[item.status] = (acc[item.status] || 0) + 1;
        return acc;
    }, {} as Record<string, number>) || {};

    const warrantyExpiring = equipment?.filter(item => {
        if (!item.warranty_expiry) return false;
        const today = new Date();
        const warrantyDate = new Date(item.warranty_expiry);
        const daysUntilExpiry = Math.ceil((warrantyDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
    }).length || 0;

    return (
        <Grid container spacing={2}>
            <Grid item xs={12} md={3}>
                <StatCard
                    title="Total Equipment"
                    value={total || 0}
                    icon={<EquipmentIcon />}
                    color="#2196f3"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <StatCard
                    title="Active"
                    value={statusCounts.active || 0}
                    icon={<CheckIcon />}
                    color="#4caf50"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <StatCard
                    title="Needs Service"
                    value={statusCounts.needs_service || 0}
                    icon={<WarningIcon />}
                    color="#f44336"
                />
            </Grid>
            <Grid item xs={12} md={3}>
                <StatCard
                    title="Warranty Expiring"
                    value={warrantyExpiring}
                    icon={<WarningIcon />}
                    color="#ff9800"
                    subtitle="Within 30 days"
                />
            </Grid>
        </Grid>
    );
};

const TechniciansOverview = () => {
    const { data: technicians, total, isPending } = useGetList<Technician>('technicians', {
        pagination: { page: 1, perPage: 1000 },
    });

    if (isPending) return <div>Loading...</div>;

    const activeTechnicians = technicians?.filter(tech => tech.status === 'active').length || 0;
    const onLeave = technicians?.filter(tech => tech.status === 'on_leave').length || 0;

    return (
        <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
                <StatCard
                    title="Total Technicians"
                    value={total || 0}
                    icon={<TechnicianIcon />}
                    color="#2196f3"
                />
            </Grid>
            <Grid item xs={12} md={4}>
                <StatCard
                    title="Active"
                    value={activeTechnicians}
                    icon={<CheckIcon />}
                    color="#4caf50"
                />
            </Grid>
            <Grid item xs={12} md={4}>
                <StatCard
                    title="On Leave"
                    value={onLeave}
                    icon={<WarningIcon />}
                    color="#ff9800"
                />
            </Grid>
        </Grid>
    );
};

export const HVACDashboard = () => {
    return (
        <Box sx={{ p: 2 }}>
            <Typography variant="h4" gutterBottom>
                HVAC Management Dashboard
            </Typography>
            
            <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                Service Orders
            </Typography>
            <ServiceOrdersOverview />
            
            <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
                Equipment
            </Typography>
            <EquipmentOverview />
            
            <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>
                Technicians
            </Typography>
            <TechniciansOverview />
        </Box>
    );
};
