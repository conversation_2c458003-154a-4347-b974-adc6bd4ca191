{"name": "atomic-crm", "version": "0.0.2", "type": "module", "types": "./lib/types", "module": "./lib/index.js", "main": "./lib/index.cjs", "files": ["lib", "README.md", "src"], "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.17.1", "@nivo/bar": "^0.88.0", "@nivo/core": "^0.88.0", "@supabase/supabase-js": "^2.49.1", "@types/jsonexport": "^3.0.5", "clsx": "^2.1.0", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "faker": "5.5.3", "lodash": "~4.17.5", "papaparse": "^5.4.1", "prop-types": "^15.8.1", "ra-data-fakerest": "^5.4.0", "ra-supabase": "^3.3.1", "react": "^18.2.0", "react-admin": "^5.4.0", "react-cropper": "^2.3.3", "react-dom": "^18.2.0", "react-error-boundary": "^5.0.0", "react-router": "^7.4.0", "react-router-dom": "^7.4.0", "serve": "^14.2.3"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.6", "@inquirer/prompts": "^7.4.0", "@storybook/addon-essentials": "^8.6.7", "@storybook/addon-interactions": "^8.6.7", "@storybook/addon-links": "^8.6.7", "@storybook/addon-onboarding": "^8.6.7", "@storybook/blocks": "^8.6.7", "@storybook/react": "^8.6.7", "@storybook/react-vite": "^8.6.7", "@storybook/test": "^8.6.7", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/faker": "^5.5.9", "@types/jest": "^29.5.14", "@types/jsonexport": "^3.0.5", "@types/lodash": "^4.17.16", "@types/papaparse": "^ 5.3.15", "@types/pg": "^8.11.11", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-storybook": "^0.11.6", "eslint-plugin-testing-library": "^5.11.0", "execa": "git+https://github.com/sindresorhus/execa", "gh-pages": "^6.1.1", "ink": "^5.0.1", "lint-staged": "^15.5.0", "pg": "^8.11.3", "prettier": "~3.5.3", "rollup-plugin-visualizer": "^5.12.0", "storybook": "^8.6.7", "supabase": "^2.19.7", "typescript": "^5.3.3", "vite": "^6.2.2", "vite-plugin-dts": "^4.5.3 ", "vitest": "^3.0.9", "web-vitals": "^4.2.4"}, "scripts": {"test": "vitest", "dev": "vite --force", "build": "tsc && vite build", "build-lib": "tsc && vite build --config vite.lib.config", "preview": "vite preview", "lint:apply": "eslint **/*.{mjs,ts,tsx} --fix", "lint:check": "eslint **/*.{mjs,ts,tsx}", "prettier:apply": "prettier --config ./.prettierrc.mjs --write --list-different \"**/*.{js,json,ts,tsx,css,md,html}\"", "prettier:check": "prettier --config ./.prettierrc.mjs --check \"**/*.{js,json,ts,tsx,css,md,html}\"", "ghpages:deploy": "node ./scripts/ghpages-deploy.mjs", "supabase:remote:init": "node ./scripts/supabase-remote-init.mjs", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}}