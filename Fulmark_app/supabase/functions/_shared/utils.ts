export const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers':
        'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, PATCH, DELETE',
};

export function createErrorResponse(status: number, message: string) {
    return new Response(JSON.stringify({ status, message }), {
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
        status,
    });
}
